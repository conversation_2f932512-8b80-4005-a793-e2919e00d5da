// src/utils/firebaseAdmin.ts
import { httpsCallable } from 'firebase/functions';
import { functions } from '../config/firebase';
import type { CustomClaims } from '../types';

// Get user with claims
export const getUserWithClaims = async (uid: string) => {
  const getUserFn = httpsCallable(functions, 'getUserWithClaims');
  const result = await getUserFn({ uid });
  return result.data;
};

// List all users
export const listAllUsers = async (maxResults = 100, pageToken?: string) => {
  const listUsersFn = httpsCallable(functions, 'listUsers');
  const result = await listUsersFn({ maxResults, pageToken });
  return result.data;
};

// Update user claims
export const updateUserClaims = async (uid: string, claims: Partial<CustomClaims>) => {
  const updateClaimsFn = httpsCallable(functions, 'updateUserClaims');
  const result = await updateClaimsFn({ uid, claims });
  return result.data;
};

// Promote user to admin
export const promoteUserToAdmin = async (uid: string) => {
  const promoteFn = httpsCallable(functions, 'promoteToAdmin');
  const result = await promoteFn({ uid });
  return result.data;
};

// Set default permissions for new user
export const setDefaultPermissions = async (uid: string, role = 'basic') => {
  const setPermissionsFn = httpsCallable(functions, 'setDefaultUserPermissions');
  const result = await setPermissionsFn({ uid, role });
  return result.data;
};
