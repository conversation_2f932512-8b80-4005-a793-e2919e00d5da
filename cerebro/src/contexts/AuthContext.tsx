// src/contexts/AuthContext.tsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  type User as FirebaseUser,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  getIdTokenResult
} from 'firebase/auth';
import { httpsCallable } from 'firebase/functions';
import { auth, functions } from '../config/firebase';
import type { AuthContextType, User, CustomClaims } from '../types';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Check if current user is admin
  const isAdmin = currentUser?.customClaims?.isAdmin || false;

  const login = async (email: string, password: string): Promise<void> => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);

      // Check if user is admin (only admins can access this app)
      const tokenResult = await getIdTokenResult(userCredential.user);
      if (!tokenResult.claims.isAdmin) {
        await signOut(auth);
        throw new Error('Access denied. Admin privileges required.');
      }
    } catch (error) {
      throw error;
    }
  };

  const logout = async (): Promise<void> => {
    return signOut(auth);
  };

  // Function to update user permissions (only for admins)
  const updateUserPermissions = async (uid: string, permissions: Partial<CustomClaims>): Promise<void> => {
    try {
      const updateClaims = httpsCallable(functions, 'updateUserClaims');
      const result = await updateClaims({ uid, claims: permissions });
      console.log('Claims updated:', result.data);
    } catch (error) {
      console.error('Error updating user permissions:', error);
      throw error;
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        try {
          // Get the latest token with custom claims
          const tokenResult = await getIdTokenResult(firebaseUser, true);

          const user: User = {
            uid: firebaseUser.uid,
            email: firebaseUser.email!,
            displayName: firebaseUser.displayName || undefined,
            customClaims: tokenResult.claims as unknown as CustomClaims,
          };

          setCurrentUser(user);
        } catch (error) {
          console.error('Error getting user claims:', error);
          setCurrentUser(null);
        }
      } else {
        setCurrentUser(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value: AuthContextType = {
    currentUser,
    loading,
    isAdmin,
    login,
    logout,
    updateUserPermissions
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};