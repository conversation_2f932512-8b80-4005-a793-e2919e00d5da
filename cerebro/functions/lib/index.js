"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setDefaultUserPermissions = exports.promoteToAdmin = exports.initializeAdmin = exports.listUsers = exports.getUserWithClaims = exports.updateUserClaims = void 0;
// functions/src/index.ts
const https_1 = require("firebase-functions/v2/https");
const https_2 = require("firebase-functions/v2/https");
const admin = __importStar(require("firebase-admin"));
const cors_1 = __importDefault(require("cors"));
// Initialize Firebase Admin
admin.initializeApp();
const corsHandler = (0, cors_1.default)({ origin: true });
// Function to update user custom claims
exports.updateUserClaims = (0, https_1.onCall)(async (request) => {
    // Check if the caller is authenticated and is an admin
    if (!request.auth) {
        throw new https_1.HttpsError('unauthenticated', 'Must be authenticated');
    }
    // Get caller's claims to check if they're admin
    const callerClaims = request.auth.token;
    if (!callerClaims.isAdmin && callerClaims.role !== 'admin') {
        throw new https_1.HttpsError('permission-denied', 'Must be admin');
    }
    const { uid, claims } = request.data;
    if (!uid || !claims) {
        throw new https_1.HttpsError('invalid-argument', 'UID and claims are required');
    }
    try {
        // Update the user's custom claims
        await admin.auth().setCustomUserClaims(uid, claims);
        // Log the change
        console.log(`Updated claims for user ${uid}:`, claims);
        return { success: true, message: 'Claims updated successfully' };
    }
    catch (error) {
        console.error('Error updating claims:', error);
        throw new https_1.HttpsError('internal', 'Failed to update claims');
    }
});
// Function to get user with claims
exports.getUserWithClaims = (0, https_1.onCall)(async (request) => {
    if (!request.auth) {
        throw new https_1.HttpsError('unauthenticated', 'Must be authenticated');
    }
    const callerClaims = request.auth.token;
    if (!callerClaims.isAdmin && callerClaims.role !== 'admin') {
        throw new https_1.HttpsError('permission-denied', 'Must be admin');
    }
    const { uid } = request.data;
    if (!uid) {
        throw new https_1.HttpsError('invalid-argument', 'UID is required');
    }
    try {
        const userRecord = await admin.auth().getUser(uid);
        return {
            uid: userRecord.uid,
            email: userRecord.email,
            displayName: userRecord.displayName,
            customClaims: userRecord.customClaims || {},
            createdAt: userRecord.metadata.creationTime,
            lastLoginAt: userRecord.metadata.lastSignInTime,
        };
    }
    catch (error) {
        console.error('Error getting user:', error);
        throw new https_1.HttpsError('internal', 'Failed to get user');
    }
});
// Function to list all users (with pagination)
exports.listUsers = (0, https_1.onCall)(async (request) => {
    if (!request.auth) {
        throw new https_1.HttpsError('unauthenticated', 'Must be authenticated');
    }
    const callerClaims = request.auth.token;
    if (!callerClaims.isAdmin && callerClaims.role !== 'admin') {
        throw new https_1.HttpsError('permission-denied', 'Must be admin');
    }
    const { maxResults = 100, pageToken } = request.data;
    try {
        const listUsersResult = await admin.auth().listUsers(maxResults, pageToken);
        const users = listUsersResult.users.map(user => ({
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
            customClaims: user.customClaims || {},
            createdAt: user.metadata.creationTime,
            lastLoginAt: user.metadata.lastSignInTime,
            disabled: user.disabled,
        }));
        return {
            users,
            pageToken: listUsersResult.pageToken,
        };
    }
    catch (error) {
        console.error('Error listing users:', error);
        throw new https_1.HttpsError('internal', 'Failed to list users');
    }
});
// Function to initialize admin user (run once)
exports.initializeAdmin = (0, https_2.onRequest)((req, res) => {
    corsHandler(req, res, async () => {
        try {
            const { email, password, adminSecret } = req.body;
            // Simple security check - you should use a proper secret
            if (adminSecret !== 'your-secret-key-change-this') {
                res.status(403).json({ error: 'Invalid admin secret' });
                return;
            }
            if (!email || !password) {
                res.status(400).json({ error: 'Email and password required' });
                return;
            }
            // Create user
            const userRecord = await admin.auth().createUser({
                email,
                password,
                emailVerified: true,
            });
            // Set admin claims
            const adminClaims = {
                role: 'admin',
                isAdmin: true,
                apps: {
                    'app-dashboard': { access: true, level: 'full' },
                    'app-reports': { access: true, level: 'full' },
                    'app-billing': { access: true, level: 'full' },
                    'app-analytics': { access: true, level: 'full' },
                    'app-api': { access: true, level: 'full' },
                },
                subscription: {
                    plan: 'enterprise',
                    status: 'active',
                },
            };
            await admin.auth().setCustomUserClaims(userRecord.uid, adminClaims);
            console.log(`Admin user created: ${email} (${userRecord.uid})`);
            res.json({
                success: true,
                uid: userRecord.uid,
                message: 'Admin user created successfully',
            });
        }
        catch (error) {
            console.error('Error creating admin:', error);
            res.status(500).json({ error: 'Failed to create admin user' });
        }
    });
});
// Function to promote existing user to admin
exports.promoteToAdmin = (0, https_1.onCall)(async (request) => {
    if (!request.auth) {
        throw new https_1.HttpsError('unauthenticated', 'Must be authenticated');
    }
    const callerClaims = request.auth.token;
    if (!callerClaims.isAdmin && callerClaims.role !== 'admin') {
        throw new https_1.HttpsError('permission-denied', 'Must be admin');
    }
    const { uid } = request.data;
    if (!uid) {
        throw new https_1.HttpsError('invalid-argument', 'UID is required');
    }
    try {
        const adminClaims = {
            role: 'admin',
            isAdmin: true,
            apps: {
                'app-dashboard': { access: true, level: 'full' },
                'app-reports': { access: true, level: 'full' },
                'app-billing': { access: true, level: 'full' },
                'app-analytics': { access: true, level: 'full' },
                'app-api': { access: true, level: 'full' },
            },
            subscription: {
                plan: 'enterprise',
                status: 'active',
            },
        };
        await admin.auth().setCustomUserClaims(uid, adminClaims);
        return { success: true, message: 'User promoted to admin' };
    }
    catch (error) {
        console.error('Error promoting user:', error);
        throw new https_1.HttpsError('internal', 'Failed to promote user');
    }
});
// Function to set default permissions for new users
exports.setDefaultUserPermissions = (0, https_1.onCall)(async (request) => {
    if (!request.auth) {
        throw new https_1.HttpsError('unauthenticated', 'Must be authenticated');
    }
    const { uid, role = 'basic' } = request.data;
    if (!uid) {
        throw new https_1.HttpsError('invalid-argument', 'UID is required');
    }
    try {
        const defaultClaims = {
            role,
            apps: {
                'app-dashboard': { access: true, level: 'read' },
            },
            subscription: {
                plan: role === 'basic' ? 'basic' : role,
                status: 'active',
            },
        };
        await admin.auth().setCustomUserClaims(uid, defaultClaims);
        return { success: true, message: 'Default permissions set' };
    }
    catch (error) {
        console.error('Error setting default permissions:', error);
        throw new https_1.HttpsError('internal', 'Failed to set permissions');
    }
});
//# sourceMappingURL=index.js.map