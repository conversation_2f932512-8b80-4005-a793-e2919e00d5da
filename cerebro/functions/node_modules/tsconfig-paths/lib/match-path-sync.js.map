{"version": 3, "file": "match-path-sync.js", "sourceRoot": "", "sources": ["../src/match-path-sync.ts"], "names": [], "mappings": ";;;AAAA,2BAA6B;AAC7B,yCAA2C;AAC3C,8CAAgD;AAChD,oCAAsC;AActC;;;;;;;GAOG;AACH,SAAgB,eAAe,CAC7B,eAAuB,EACvB,KAAuC,EACvC,UAA+B,EAC/B,WAA2B;IAD3B,2BAAA,EAAA,cAAwB,MAAM,CAAC;IAC/B,4BAAA,EAAA,kBAA2B;IAE3B,IAAM,aAAa,GAAG,YAAY,CAAC,yBAAyB,CAC1D,eAAe,EACf,KAAK,EACL,WAAW,CACZ,CAAC;IAEF,OAAO,UACL,eAAuB,EACvB,QAAkC,EAClC,UAAsC,EACtC,UAA0B;QAE1B,OAAA,sBAAsB,CACpB,aAAa,EACb,eAAe,EACf,QAAQ,EACR,UAAU,EACV,UAAU,EACV,UAAU,CACX;IAPD,CAOC,CAAC;AACN,CAAC;AA1BD,0CA0BC;AAED;;;;;;;;;GASG;AACH,SAAgB,sBAAsB,CACpC,oBAA8D,EAC9D,eAAuB,EACvB,QAAmE,EACnE,UAAiE,EACjE,UAA2D,EAC3D,UAA+B;IAH/B,yBAAA,EAAA,WAAoC,UAAU,CAAC,oBAAoB;IACnE,2BAAA,EAAA,aAAwC,UAAU,CAAC,cAAc;IACjE,2BAAA,EAAA,aAA4B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;IAC3D,2BAAA,EAAA,cAAwB,MAAM,CAAC;IAE/B,IAAM,QAAQ,GAAG,OAAO,CAAC,aAAa,CACpC,UAAU,EACV,oBAAoB,EACpB,eAAe,CAChB,CAAC;IAEF,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AAC3E,CAAC;AAnBD,wDAmBC;AAED,SAAS,oCAAoC,CAC3C,WAAmC,EACnC,UAAoB,EACpB,eAAuB,EACvB,UAAqC;IAErC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACtD,IAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;QACxC,IAAM,gBAAgB,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC;QACpD,IAAI,gBAAgB,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;YAC5D,IAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CACjC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAC7B,gBAAgB,CACjB,CAAC;YACF,IAAI,UAAU,CAAC,iBAAiB,CAAC,EAAE;gBACjC,OAAO,iBAAiB,CAAC;aAC1B;SACF;KACF;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,qBAAqB,CAC5B,QAAwC,EACxC,QAAmE,EACnE,UAAqC,EACrC,UAA+B;IAF/B,yBAAA,EAAA,WAAoC,UAAU,CAAC,oBAAoB;IAEnE,2BAAA,EAAA,cAAwB,MAAM,CAAC;IAE/B,KAAsB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE;QAA3B,IAAM,OAAO,iBAAA;QAChB,IACE,OAAO,CAAC,IAAI,KAAK,MAAM;YACvB,OAAO,CAAC,IAAI,KAAK,WAAW;YAC5B,OAAO,CAAC,IAAI,KAAK,OAAO,EACxB;YACA,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC5B,OAAO,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;aACzC;SACF;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YACrC,IAAM,WAAW,GAA2B,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnE,IAAI,WAAW,EAAE;gBACf,IAAM,mBAAmB,GAAG,oCAAoC,CAC9D,WAAW,EACX,UAAU,EACV,OAAO,CAAC,IAAI,EACZ,UAAU,CACX,CAAC;gBACF,IAAI,mBAAmB,EAAE;oBACvB,OAAO,mBAAmB,CAAC;iBAC5B;aACF;SACF;aAAM;YACL,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC/C;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC"}